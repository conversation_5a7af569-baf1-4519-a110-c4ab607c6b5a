import 'package:flutter/material.dart';
import 'package:mobil_kochat/core/theme/app_colors.dart';
import 'package:mobil_kochat/core/utils/app_constants.dart';
import 'package:mobil_kochat/core/utils/app_functions.dart';
import '../../../../di/dependency_injection.dart';
import '../../../auth/presentation/pages/login_page.dart';
import '../../models/customer_model.dart';
import '../../services/customer_service.dart';
import 'customer_form_page.dart';
import '../../../auth/services/user_profile_service.dart';
import '../../../../core/services/logout_service.dart';

class CustomerPage extends StatefulWidget {
  const CustomerPage({super.key});

  @override
  State<CustomerPage> createState() => _CustomerPageState();
}

class _CustomerPageState extends State<CustomerPage> {
  final CustomerService _customerService = CustomerService(
      dio: di(), // Your Dio instance,
      storage: di(), // Your GetStorage instance,
      networkInfo: di() // Your NetworkInfo instance,
      );
  final UserProfileService _userProfileService = di<UserProfileService>();
  final LogoutService _logoutService = di<LogoutService>();

  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Customer> _customers = [];
  bool _isLoading = false;
  bool _isRefreshing = false; // Add this to track refresh state separately
  bool _isSearching = false;
  bool _hasMore = true;
  int _currentPage = 1;
  String _lastSearchQuery = '';

  // User profile data
  String _userName = 'Foydalanuvchi';
  String _userPhone = '';

  @override
  void initState() {
    super.initState();
    _loadCustomers();
    _loadUserProfile();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreCustomers();
    }
  }

  Future<void> _loadUserProfile() async {
    try {
      final userInfo = await _userProfileService.getUserDisplayInfo();
      if (mounted) {
        setState(() {
          _userName = userInfo['name'] ?? 'Foydalanuvchi';
          _userPhone = userInfo['phone'] ?? '';
        });
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  Future<void> _handleLogout() async {
    LogoutService logoutService = di<LogoutService>();
    try {
      // Show confirmation dialog
      final shouldLogout = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Chiqish'),
          content: const Text('Haqiqatan ham tizimdan chiqmoqchimisiz?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Bekor qilish'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop(true);
                Navigator.of(this.context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const LoginPage()),
                      (route) => false,
                );
                await logoutService.performCompleteLogout();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Chiqish'),
            ),
          ],
        ),
      );

      if (shouldLogout == true && mounted) {
        await _logoutService.performLogout(context: context);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Chiqishda xatolik yuz berdi: $e');
      }
    }
  }

  Future<void> _loadCustomers({bool refresh = false}) async {
    if (_isLoading && !refresh) return; // Allow refresh even if loading

    setState(() {
      if (refresh) {
        _isRefreshing = true; // Set refresh state instead of loading
        _customers.clear();
        _currentPage = 1;
        _hasMore = true;
      } else {
        _isLoading = true; // Only set loading for non-refresh operations
      }
    });

    try {
      // Add minimum delay for refresh indicator visibility
      if (refresh) {
        await Future.delayed(const Duration(milliseconds: 300));
      }

      final response = await _customerService.getCustomers(
        page: _currentPage,
        limit: 20,
        search: _lastSearchQuery.isNotEmpty ? _lastSearchQuery : null,
        forceRefresh: refresh,
      );

      if (response != null) {
        if (mounted) {
          setState(() {
            if (refresh || _currentPage == 1) {
              _customers = response.docs;
            } else {
              _customers.addAll(response.docs);
            }
            _hasMore = response.hasNextPage;
            if (_hasMore) {
              _currentPage = response.nextPage ?? _currentPage + 1;
            }
          });
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Xatolik yuz berdi: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isRefreshing = false; // Reset both states
        });
      }
    }
  }

  Future<void> _loadMoreCustomers() async {
    if (!_hasMore || _isLoading) return;
    await _loadCustomers();
  }

  Future<void> _searchCustomers(String query) async {
    if (_isSearching) return;

    setState(() {
      _isSearching = true;
      _lastSearchQuery = query.trim();
      _customers = [];
      _currentPage = 1;
      _hasMore = true;
    });

    try {
      await _loadCustomers(refresh: true);
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Qidirishda xatolik yuz berdi: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message, style: const TextStyle(color: Colors.white)),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Fixed refresh method with proper Future handling
  Future<void> _refreshCustomers() async {
    try {
      // Ensure minimum delay for smooth UX
      final refreshFuture = _loadCustomers(refresh: true);
      final minimumDelay = Future.delayed(const Duration(milliseconds: 800));

      // Wait for both to complete
      await Future.wait([refreshFuture, minimumDelay]);
    } catch (e) {
      print('Refresh error: $e');
    }
  }

  Widget _buildCustomerCard(Customer customer) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: AppColors.cFirstColor,
          child: Text(
            customer.fullName.isNotEmpty
                ? customer.fullName[0].toUpperCase()
                : 'M',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          customer.fullName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('📞 ${_formatPhoneNumber(customer.phone)}'),
            const SizedBox(height: 2),
            Text('📍 ${customer.address}'),
            if (customer.passport.isNotEmpty) ...[
              const SizedBox(height: 2),
              Text('🆔 ${customer.jshshir}'),
            ],
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CustomerFormPage(customer: customer),
            ),
          );
          if (result == true && mounted) {
            _refreshCustomers();
          }
        },
      ),
    );
  }

  String _formatPhoneNumber(String phone) {
    // Remove any existing formatting
    String cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // Handle different input formats
    if (cleanPhone.startsWith('998')) {
      cleanPhone = cleanPhone.substring(3); // Remove country code if present
    }

    // Ensure we have 9 digits for Uzbekistan mobile numbers
    if (cleanPhone.length == 9) {
      // Format as: +998 XX XXX-XX-XX
      return '+998 ${cleanPhone.substring(0, 2)} ${cleanPhone.substring(2, 5)}-${cleanPhone.substring(5, 7)}-${cleanPhone.substring(7, 9)}';
    }

    // If the phone number doesn't match expected format, return with +998 prefix
    return '+998 $cleanPhone';
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _lastSearchQuery.isNotEmpty
                ? 'Qidirilgan mijoz topilmadi'
                : 'Mijozlar ro\'yxati bo\'sh',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _lastSearchQuery.isNotEmpty
                ? 'Boshqa kalit so\'z bilan qidiring'
                : 'Yangi mijoz qo\'shish uchun "+" tugmasini bosing',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRefreshableContent() {
    // If refreshing, show minimal content to let RefreshIndicator handle the loading
    if (_isRefreshing) {
      return ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            // Don't show anything here, let RefreshIndicator show the loading
          ),
        ],
      );
    }

    if (_customers.isEmpty) {
      // Show loading only for initial load (not refresh)
      if (_isLoading) {
        return ListView(
          physics: const AlwaysScrollableScrollPhysics(),
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.6,
              child: _buildLoadingIndicator(),
            ),
          ],
        );
      } else {
        // Show empty state only when not loading and not refreshing
        return ListView(
          physics: const AlwaysScrollableScrollPhysics(),
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.6,
              child: _buildEmptyState(),
            ),
          ],
        );
      }
    }

    // Show customer list with pagination loading at bottom
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(bottom: 80),
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: _customers.length +
          (_hasMore && _isLoading && !_isRefreshing ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _customers.length) {
          return _buildLoadingIndicator();
        }
        return _buildCustomerCard(_customers[index]);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton.extended(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CustomerFormPage()),
          );
          if (result == true && mounted) {
            _refreshCustomers();
          }
        },
        foregroundColor: Colors.white,
        backgroundColor: AppColors.cFirstColor,
        icon: const Icon(Icons.person_add),
        label: const Text(
          "Yangi mijoz",
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      body: Column(
        children: [
          // Search Header with User Profile
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8)
                .copyWith(top: 50),
            width: double.infinity,
            height: 220,
            decoration: BoxDecoration(
              color: AppColors.cFirstColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(cRadius22),
                bottomRight: Radius.circular(cRadius22),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // User Profile Section
                const SizedBox(height: 10),
                Row(
                  children: [
                    // User Avatar
                    CircleAvatar(
                      radius: 25,
                      backgroundColor: Colors.white,
                      child: CircleAvatar(
                        radius: 23,
                        backgroundColor:
                            AppColors.cFirstColor.withValues(alpha: 0.8),
                        child: Text(
                          _userName.isNotEmpty
                              ? _userName[0].toUpperCase()
                              : 'U',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // User Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _userName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (_userPhone.isNotEmpty) ...[
                            const SizedBox(height: 2),
                            Text(
                              _userPhone,
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                    // Logout Button
                    IconButton(
                      onPressed: _handleLogout,
                      icon: const Icon(
                        Icons.logout,
                        color: Colors.white,
                        size: 24,
                      ),
                      tooltip: 'Chiqish',
                    ),
                  ],
                ),
                const SizedBox(height: 35),
                // Search Field
                TextField(
                  controller: _searchController,
                  style: const TextStyle(color: Colors.black87),
                  onChanged: (value) {
                    // Debounce search to avoid too many API calls
                    Future.delayed(const Duration(milliseconds: 500), () {
                      if (_searchController.text == value && mounted) {
                        _searchCustomers(value);
                      }
                    });
                  },
                  decoration: InputDecoration(
                    hintText: 'ISM, JSHSHIR...',
                    hintStyle: TextStyle(color: Colors.grey[600], fontSize: 14),
                    prefixIcon: _isSearching
                        ? Padding(
                            padding: const EdgeInsets.all(12),
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.grey[600]!,
                                ),
                              ),
                            ),
                          )
                        : Icon(
                            Icons.search,
                            color: Colors.grey[600],
                          ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: Icon(
                              Icons.clear,
                              color: Colors.grey[600],
                            ),
                            onPressed: () {
                              _searchController.clear();
                              _searchCustomers('');
                            },
                          )
                        : null,
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                      borderSide: BorderSide(
                        color: AppColors.cFirstColor,
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Customer List with RefreshIndicator
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshCustomers,
              displacement: 40.0,
              // Distance from top before triggering
              strokeWidth: 2.0,
              color: AppColors.cFirstColor,
              backgroundColor: Colors.white,
              child: _buildRefreshableContent(),
            ),
          ),
        ],
      ),
    );
  }
}
