import 'dart:io';
import 'dart:typed_data';

import '../../customer/models/customer_model.dart';
import '../../location/models/district_model.dart';
import '../../tree/models/tree_variety_model.dart';
import '../presentation/bloc/orders_bloc/orders_bloc.dart';
import '../presentation/pages/orders_page.dart';

class Order {
  final String id;
  final Customer? client;
  final String worker;
  final List<String> photos;
  final Location location;
  final List<OrderProduct> products;
  final double totalPrice;
  final int paymentType;
  final String date;
  final String time;
  final int status;
  final Province? province;
  final Region? region;
  final District? district;
  final String? pdf;
  final DateTime createdAt;
  final DateTime updatedAt;

  Order({
    required this.id,
    this.client,
    required this.worker,
    required this.photos,
    required this.location,
    required this.products,
    required this.totalPrice,
    required this.paymentType,
    required this.date,
    required this.time,
    required this.status,
    this.province,
    this.region,
    this.district,
    this.pdf,
    required this.createdAt,
    required this.updatedAt,
  });

  String get statusText {
    switch (status) {
      case 1:
        return 'yangi'; // new
      case 2:
        return 'yakunlangan'; // completed
      case 3:
        return 'bekor qilingan'; // cancelled
      default:
        return 'noma\'lum';
    }
  }

  String get paymentTypeText {
    switch (paymentType) {
      case 1:
        return 'Naqd'; // Cash
      case 2:
        return 'Nasiya'; // Credit
      default:
        return 'Noma\'lum';
    }
  }

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['_id']?.toString() ?? '',
      client: json['client'] != null ? Customer.fromJson(json['client']) : null,
      worker: json['worker']?.toString() ?? '',
      photos: List<String>.from(json['photos'] ?? []),
      location: Location.fromJson(json['location'] ?? {}),
      products: (json['products'] as List? ?? [])
          .map((p) => OrderProduct.fromJson(p))
          .toList(),
      totalPrice: (json['totalPrice'] ?? 0).toDouble(),
      paymentType: json['paymentType'] ?? 1,
      date: json['date']?.toString() ?? '',
      time: json['time']?.toString() ?? '',
      status: json['status'] ?? 1,
      province: json['province'] != null ? Province.fromJson(json['province']) : null,
      region: json['region'] != null ? Region.fromJson(json['region']) : null,
      district: json['district'] != null ? District.fromJson(json['district']) : null,
      pdf: json['pdf']?.toString(),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class Location {
  final String longitude;
  final String latitude;

  Location({
    required this.longitude,
    required this.latitude,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      longitude: json['longitude']?.toString() ?? '0.0',
      latitude: json['latitude']?.toString() ?? '0.0',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'longitude': longitude,
      'latitude': latitude,
    };
  }
}

class OrderProduct {
  final String id;
  final Tree? tree;
  final int count;
  final double price;
  final double totalPrice;

  OrderProduct({
    required this.id,
    this.tree,
    required this.count,
    required this.price,
    required this.totalPrice,
  });

  factory OrderProduct.fromJson(Map<String, dynamic> json) {
    return OrderProduct(
      id: json['_id']?.toString() ?? '',
      tree: json['tree'] != null ? Tree.fromJson(json['tree']) : null,
      count: json['count'] ?? 0,
      price: (json['price'] ?? 0).toDouble(),
      totalPrice: (json['totalPrice'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tree': tree?.id,
      'count': count,
      'price': price,
      'totalPrice': totalPrice,
    };
  }
}

class CreateOrderRequest {
  final String client;
  final List<File> photos;
  final double totalPrice;
  final int paymentType;
  final String province;
  final String region;
  final String district;
  final File? contractFile;
  final List<TreeProduct> products;
  final Location location;
  final Uint8List? signature; // Base64 encoded signature

  CreateOrderRequest({
    required this.client,
    required this.photos,
    required this.totalPrice,
    required this.paymentType,
    required this.province,
    required this.region,
    required this.district,
    this.contractFile,
    required this.products,
    required this.location,
    this.signature,
  });
}

/// Orders paginated response
class OrdersResponse {
  final List<Order> orders;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final bool hasNextPage;
  final bool hasPrevPage;

  OrdersResponse({
    required this.orders,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory OrdersResponse.fromJson(Map<String, dynamic> json) {
    return OrdersResponse(
      orders: (json['docs'] as List<dynamic>? ?? [])
          .map((order) => Order.fromJson(order))
          .toList(),
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 1,
      page: json['page'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }
}

/// Product for order creation
class CreateOrderProduct {
  final String treeId;
  final int count;
  final double price;
  final double totalPrice;

  CreateOrderProduct({
    required this.treeId,
    required this.count,
    required this.price,
    required this.totalPrice,
  });
}

/// Location data model
class LocationData {
  final String latitude;
  final String longitude;

  LocationData({
    required this.latitude,
    required this.longitude,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      latitude: json['latitude']?.toString() ?? '0',
      longitude: json['longitude']?.toString() ?? '0',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

/// Payment type enum
enum PaymentType {
  cash,
  credit,
}

/// Clients paginated response
class ClientsResponse {
  final List<Customer> clients;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final bool hasNextPage;
  final bool hasPrevPage;

  ClientsResponse({
    required this.clients,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory ClientsResponse.fromJson(Map<String, dynamic> json) {
    return ClientsResponse(
      clients: (json['docs'] as List<dynamic>? ?? [])
          .map((client) => Customer.fromJson(client))
          .toList(),
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 1,
      page: json['page'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }
}

/// Tree model
class Tree {
  final String id;
  final String name;
  final String variety;
  final double price; // Unit price per tree
  final int? count; // Available quantity/stock (nullable)
  final String? image;
  final String? description;

  Tree({
    required this.id,
    required this.name,
    required this.variety,
    required this.price,
    this.count,
    this.image,
    this.description,
  });

  factory Tree.fromJson(Map<String, dynamic> json) {
    // Handle variety field - it might be a string or an object
    String varietyValue = '';
    if (json['variety'] != null) {
      if (json['variety'] is String) {
        varietyValue = json['variety'];
      } else if (json['variety'] is Map) {
        // If variety is an object, try to get the title or name
        final varietyMap = json['variety'] as Map<String, dynamic>;
        varietyValue = varietyMap['title'] ?? varietyMap['name'] ?? '';
      }
    }

    return Tree(
      id: json['_id'] ?? '',
      name: json['name'] ?? json['title'] ?? '',
      variety: varietyValue,
      price: (json['price'] ?? 0).toDouble(),
      count: json['count'] is int ? json['count'] : (json['count'] is num ? (json['count'] as num).toInt() : null),
      image: json['image'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'variety': variety,
      'price': price,
      'count': count,
      'image': image,
      'description': description,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Tree && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Trees paginated response
class TreesResponse {
  final List<Tree> trees;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final bool hasNextPage;
  final bool hasPrevPage;

  TreesResponse({
    required this.trees,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory TreesResponse.fromJson(Map<String, dynamic> json) {
    List<Tree> trees = [];
    if (json['docs'] != null) {
      final docs = json['docs'] as List<dynamic>;
      for (var treeJson in docs) {
        try {
          trees.add(Tree.fromJson(treeJson));
        } catch (e) {
          // Skip invalid trees instead of failing completely
          print('Warning: Skipping invalid tree data: $e');
        }
      }
    }

    return TreesResponse(
      trees: trees,
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 1,
      page: json['page'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }
}

/// Provinces paginated response
class ProvincesResponse {
  final List<Province> provinces;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final bool hasNextPage;
  final bool hasPrevPage;

  ProvincesResponse({
    required this.provinces,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory ProvincesResponse.fromJson(Map<String, dynamic> json) {
    return ProvincesResponse(
      provinces: (json['docs'] as List<dynamic>? ?? [])
          .map((province) => Province.fromJson(province))
          .toList(),
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 1,
      page: json['page'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }
}


/// Regions paginated response
class RegionsResponse {
  final List<Region> regions;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final bool hasNextPage;
  final bool hasPrevPage;

  RegionsResponse({
    required this.regions,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory RegionsResponse.fromJson(Map<String, dynamic> json) {
    return RegionsResponse(
      regions: (json['docs'] as List<dynamic>? ?? [])
          .map((region) => Region.fromJson(region))
          .toList(),
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 1,
      page: json['page'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }
}

/// Districts paginated response
class DistrictsResponse {
  final List<District> districts;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final bool hasNextPage;
  final bool hasPrevPage;

  DistrictsResponse({
    required this.districts,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory DistrictsResponse.fromJson(Map<String, dynamic> json) {
    return DistrictsResponse(
      districts: (json['docs'] as List<dynamic>? ?? [])
          .map((district) => District.fromJson(district))
          .toList(),
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 1,
      page: json['page'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }
}

/// Varieties paginated response
class VarietiesResponse {
  final List<TreeVariety> varieties;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final bool hasNextPage;
  final bool hasPrevPage;

  VarietiesResponse({
    required this.varieties,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory VarietiesResponse.fromJson(Map<String, dynamic> json) {
    return VarietiesResponse(
      varieties: (json['docs'] as List<dynamic>? ?? [])
          .map((variety) => TreeVariety.fromJson(variety))
          .toList(),
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 1,
      page: json['page'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }
}