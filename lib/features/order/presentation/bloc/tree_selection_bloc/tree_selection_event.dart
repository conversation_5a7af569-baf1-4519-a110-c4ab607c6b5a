part of 'tree_selection_bloc.dart';

abstract class TreeSelectionEvent {}

/// Add a new tree product to the list
class AddTreeProduct extends TreeSelectionEvent {
  AddTreeProduct();
}

/// Remove a tree product from the list
class RemoveTreeProduct extends TreeSelectionEvent {
  final String productId;
  
  RemoveTreeProduct(this.productId);
}

/// Update a specific tree product
class UpdateTreeProduct extends TreeSelectionEvent {
  final String productId;
  final TreeVariety? variety;
  final Tree? tree;
  final int? count;
  final double? price;
  
  UpdateTreeProduct({
    required this.productId,
    this.variety,
    this.tree,
    this.count,
    this.price,
  });
}

/// Load trees for a specific variety and product
class LoadTreesForProduct extends TreeSelectionEvent {
  final String productId;
  final String varietyId;
  
  LoadTreesForProduct({
    required this.productId,
    required this.varietyId,
  });
}

/// Clear all tree products (for new order)
class ClearAllTreeProducts extends TreeSelectionEvent {}

/// Validate all tree products
class ValidateTreeProducts extends TreeSelectionEvent {}

/// Clear error state
class ClearTreeSelectionError extends TreeSelectionEvent {}
