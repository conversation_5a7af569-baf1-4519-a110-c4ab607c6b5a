part of 'tree_selection_bloc.dart';

class TreeSelectionState {
  final List<TreeProduct> treeProducts;
  final Map<String, List<Tree>> availableTreesByProduct; // productId -> trees
  final Map<String, bool> loadingStatesByProduct; // productId -> isLoading
  final String? error;
  final bool isValidating;
  final List<String> invalidProductIds;

  const TreeSelectionState({
    this.treeProducts = const [],
    this.availableTreesByProduct = const {},
    this.loadingStatesByProduct = const {},
    this.error,
    this.isValidating = false,
    this.invalidProductIds = const [],
  });

  /// Get total price of all tree products
  double get totalPrice {
    return treeProducts.fold(0.0, (sum, product) => sum + product.totalPrice);
  }

  /// Check if a specific product is loading trees
  bool isProductLoading(String productId) {
    return loadingStatesByProduct[productId] ?? false;
  }

  /// Get available trees for a specific product
  List<Tree> getAvailableTreesForProduct(String productId) {
    return availableTreesByProduct[productId] ?? [];
  }

  /// Check if all products are valid
  bool get areAllProductsValid {
    return invalidProductIds.isEmpty && treeProducts.isNotEmpty;
  }

  /// Get tree product by ID
  TreeProduct? getTreeProduct(String productId) {
    try {
      return treeProducts.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  TreeSelectionState copyWith({
    List<TreeProduct>? treeProducts,
    Map<String, List<Tree>>? availableTreesByProduct,
    Map<String, bool>? loadingStatesByProduct,
    String? error,
    bool? isValidating,
    List<String>? invalidProductIds,
  }) {
    return TreeSelectionState(
      treeProducts: treeProducts ?? this.treeProducts,
      availableTreesByProduct: availableTreesByProduct ?? this.availableTreesByProduct,
      loadingStatesByProduct: loadingStatesByProduct ?? this.loadingStatesByProduct,
      error: error,
      isValidating: isValidating ?? this.isValidating,
      invalidProductIds: invalidProductIds ?? this.invalidProductIds,
    );
  }
}
