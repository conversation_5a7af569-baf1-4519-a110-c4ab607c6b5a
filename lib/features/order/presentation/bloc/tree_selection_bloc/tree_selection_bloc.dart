import 'package:bloc/bloc.dart';
import '../../../../tree/models/tree_variety_model.dart';
import '../../../models/order_model.dart';
import '../../../datasources/order_remote_datasources.dart';
import '../orders_bloc/orders_bloc.dart';

part 'tree_selection_event.dart';
part 'tree_selection_state.dart';

class TreeSelectionBloc extends Bloc<TreeSelectionEvent, TreeSelectionState> {
  final OrderRemoteDatasourceImpl orderRemoteDatasource;

  TreeSelectionBloc({required this.orderRemoteDatasource}) : super(const TreeSelectionState()) {
    on<AddTreeProduct>(_onAddTreeProduct);
    on<RemoveTreeProduct>(_onRemoveTreeProduct);
    on<UpdateTreeProduct>(_onUpdateTreeProduct);
    on<LoadTreesForProduct>(_onLoadTreesForProduct);
    on<ClearAllTreeProducts>(_onClearAllTreeProducts);
    on<ValidateTreeProducts>(_onValidateTreeProducts);
    on<ClearTreeSelectionError>(_onClearTreeSelectionError);
  }

  void _onAddTreeProduct(AddTreeProduct event, Emitter<TreeSelectionState> emit) {
    final newProduct = TreeProduct(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      treeId: '',
      treeType: '',
      variety: null,
      count: 1,
      price: 0,
      totalPrice: 0,
    );

    final updatedProducts = [...state.treeProducts, newProduct];
    emit(state.copyWith(treeProducts: updatedProducts));
  }

  void _onRemoveTreeProduct(RemoveTreeProduct event, Emitter<TreeSelectionState> emit) {
    final updatedProducts = state.treeProducts
        .where((product) => product.id != event.productId)
        .toList();

    // Clean up loading states and available trees for removed product
    final updatedLoadingStates = Map<String, bool>.from(state.loadingStatesByProduct);
    final updatedAvailableTrees = Map<String, List<Tree>>.from(state.availableTreesByProduct);

    updatedLoadingStates.remove(event.productId);
    updatedAvailableTrees.remove(event.productId);

    emit(state.copyWith(
      treeProducts: updatedProducts,
      loadingStatesByProduct: updatedLoadingStates,
      availableTreesByProduct: updatedAvailableTrees,
    ));
  }

  void _onUpdateTreeProduct(UpdateTreeProduct event, Emitter<TreeSelectionState> emit) {
    final updatedProducts = state.treeProducts.map((product) {
      if (product.id == event.productId) {
        final newCount = event.count ?? product.count;
        final newPrice = event.price ?? product.price;
        final newTree = event.tree;
        final newVariety = event.variety ?? product.variety;

        // If tree is provided, use its price
        final finalPrice = newTree?.price ?? newPrice;

        return product.copyWith(
          treeId: newTree?.id ?? product.treeId,
          treeType: newTree?.name ?? product.treeType,
          variety: newVariety,
          count: newCount,
          price: finalPrice,
          totalPrice: newCount * finalPrice,
        );
      }
      return product;
    }).toList();

    emit(state.copyWith(treeProducts: updatedProducts));
  }

  Future<void> _onLoadTreesForProduct(LoadTreesForProduct event, Emitter<TreeSelectionState> emit) async {
    // Set loading state for this specific product
    final updatedLoadingStates = Map<String, bool>.from(state.loadingStatesByProduct);
    updatedLoadingStates[event.productId] = true;

    emit(state.copyWith(loadingStatesByProduct: updatedLoadingStates));

    try {
      final response = await orderRemoteDatasource.getAllTrees(variety: event.varietyId);

      // Update available trees for this product
      final updatedAvailableTrees = Map<String, List<Tree>>.from(state.availableTreesByProduct);
      updatedAvailableTrees[event.productId] = response.trees;

      // Clear loading state
      updatedLoadingStates[event.productId] = false;

      emit(state.copyWith(
        availableTreesByProduct: updatedAvailableTrees,
        loadingStatesByProduct: updatedLoadingStates,
        error: null,
      ));
    } catch (e) {
      // Clear loading state on error
      updatedLoadingStates[event.productId] = false;

      emit(state.copyWith(
        loadingStatesByProduct: updatedLoadingStates,
        error: 'Daraxtlarni yuklashda xatolik: $e',
      ));
    }
  }

  void _onClearAllTreeProducts(ClearAllTreeProducts event, Emitter<TreeSelectionState> emit) {
    emit(const TreeSelectionState());
  }

  void _onValidateTreeProducts(ValidateTreeProducts event, Emitter<TreeSelectionState> emit) {
    emit(state.copyWith(isValidating: true));

    final invalidIds = <String>[];

    for (final product in state.treeProducts) {
      final isInvalid = product.treeId.isEmpty ||
          product.treeType.isEmpty ||
          product.variety == null ||
          product.count <= 0 ||
          product.price <= 0;

      if (isInvalid) {
        invalidIds.add(product.id);
      }
    }

    emit(state.copyWith(
      isValidating: false,
      invalidProductIds: invalidIds,
    ));
  }

  void _onClearTreeSelectionError(ClearTreeSelectionError event, Emitter<TreeSelectionState> emit) {
    emit(state.copyWith(error: null));
  }

  // Helper method to clear error
  void clearError() {
    add(ClearTreeSelectionError());
  }
}
