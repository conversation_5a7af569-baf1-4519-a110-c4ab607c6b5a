// orders_state.dart
part of 'orders_bloc.dart';

// Sentinel value for copyWith to distinguish between null and undefined
const Object _undefined = Object();

class OrdersState {
  final bool isLoading;
  final bool isLoadingMore;
  final bool isCreating;
  final List<Order> orders;
  final String? error;
  final String? successMessage;
  final bool hasMore;
  final int currentPage;
  final int totalPages;
  final String? currentStatus;
  final String? currentDate;
  final String? searchQuery;

  // New loading states
  final bool isLoadingProvinces;
  final bool isLoadingRegions;
  final bool isLoadingDistricts;
  final bool isLoadingTrees;
  final bool isLoadingClients;
  final bool isLoadingVarieties;

  // New data lists
  final List<Province>? provinces;
  final List<Region>? regions;
  final List<District>? districts;
  final List<Tree>? trees;
  final List<Customer>? clients;
  final List<TreeVariety>? varieties;

  // Selected address data
  final Province? selectedProvince;
  final Region? selectedRegion;
  final District? selectedDistrict;

  // Tree selection state
  final List<TreeProduct>? orderTreeProducts;
  final Map<String, List<Tree>>? availableTreesByProduct;
  final Map<String, bool>? treeLoadingStatesByProduct;
  final bool isValidatingTreeProducts;

  OrdersState({
    this.isLoading = false,
    this.isLoadingMore = false,
    this.isCreating = false,
    this.orders = const [],
    this.error,
    this.successMessage,
    this.hasMore = false,
    this.currentPage = 1,
    this.totalPages = 1,
    this.currentStatus,
    this.currentDate,
    this.searchQuery,
    // New loading states
    this.isLoadingProvinces = false,
    this.isLoadingRegions = false,
    this.isLoadingDistricts = false,
    this.isLoadingTrees = false,
    this.isLoadingClients = false,
    this.isLoadingVarieties = false,
    // New data lists
    this.provinces,
    this.regions,
    this.districts,
    this.trees,
    this.clients,
    this.varieties,
    // Selected address data
    this.selectedProvince,
    this.selectedRegion,
    this.selectedDistrict,
    // Tree selection state
    this.orderTreeProducts,
    this.availableTreesByProduct,
    this.treeLoadingStatesByProduct,
    this.isValidatingTreeProducts = false,
  });

  OrdersState copyWith({
    bool? isLoading,
    bool? isLoadingMore,
    bool? isCreating,
    List<Order>? orders,
    String? error,
    String? successMessage,
    bool? hasMore,
    int? currentPage,
    int? totalPages,
    String? currentStatus,
    String? currentDate,
    String? searchQuery,
    // New loading states
    bool? isLoadingProvinces,
    bool? isLoadingRegions,
    bool? isLoadingDistricts,
    bool? isLoadingTrees,
    bool? isLoadingClients,
    bool? isLoadingVarieties,
    // New data lists
    List<Province>? provinces,
    Object? regions = _undefined,
    Object? districts = _undefined,
    List<Tree>? trees,
    List<Customer>? clients,
    List<TreeVariety>? varieties,
    // Selected address data - using Object? to allow explicit null values
    Object? selectedProvince = _undefined,
    Object? selectedRegion = _undefined,
    Object? selectedDistrict = _undefined,
    // Tree selection state
    List<TreeProduct>? orderTreeProducts,
    Map<String, List<Tree>>? availableTreesByProduct,
    Map<String, bool>? treeLoadingStatesByProduct,
    bool? isValidatingTreeProducts,
  }) {
    return OrdersState(
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isCreating: isCreating ?? this.isCreating,
      orders: orders ?? this.orders,
      error: error,
      successMessage: successMessage,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      currentStatus: currentStatus ?? this.currentStatus,
      currentDate: currentDate ?? this.currentDate,
      searchQuery: searchQuery ?? this.searchQuery,
      // New loading states
      isLoadingProvinces: isLoadingProvinces ?? this.isLoadingProvinces,
      isLoadingRegions: isLoadingRegions ?? this.isLoadingRegions,
      isLoadingDistricts: isLoadingDistricts ?? this.isLoadingDistricts,
      isLoadingTrees: isLoadingTrees ?? this.isLoadingTrees,
      isLoadingClients: isLoadingClients ?? this.isLoadingClients,
      isLoadingVarieties: isLoadingVarieties ?? this.isLoadingVarieties,
      // New data lists - handle nullable lists properly
      provinces: provinces ?? this.provinces,
      regions: regions == _undefined ? this.regions : regions as List<Region>?,
      districts: districts == _undefined ? this.districts : districts as List<District>?,
      trees: trees ?? this.trees,
      clients: clients ?? this.clients,
      varieties: varieties ?? this.varieties,
      // Selected address data - handle explicit null values
      selectedProvince: selectedProvince == _undefined ? this.selectedProvince : selectedProvince as Province?,
      selectedRegion: selectedRegion == _undefined ? this.selectedRegion : selectedRegion as Region?,
      selectedDistrict: selectedDistrict == _undefined ? this.selectedDistrict : selectedDistrict as District?,
      // Tree selection state
      orderTreeProducts: orderTreeProducts ?? this.orderTreeProducts,
      availableTreesByProduct: availableTreesByProduct ?? this.availableTreesByProduct,
      treeLoadingStatesByProduct: treeLoadingStatesByProduct ?? this.treeLoadingStatesByProduct,
      isValidatingTreeProducts: isValidatingTreeProducts ?? this.isValidatingTreeProducts,
    );
  }
}