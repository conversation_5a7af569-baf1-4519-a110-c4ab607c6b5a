import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import 'package:mobil_kochat/core/theme/app_colors.dart';
import 'package:signature/signature.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:get_storage/get_storage.dart';
import 'dart:io';
import 'dart:typed_data';

import '../../../../core/utils/api_path.dart';
import '../../../../core/utils/app_constants.dart';
import '../../../../core/utils/app_functions.dart';
import '../../../customer/models/customer_model.dart';
import '../../../customer/services/customer_service.dart';
import '../../../location/models/district_model.dart';

import '../../../tree/models/tree_variety_model.dart';
import '../../../../di/dependency_injection.dart' as di;

import '../../models/order_model.dart';
import '../bloc/orders_bloc/orders_bloc.dart';
import 'order_detail_page.dart';

class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage> {
  String? selectedStatus;
  String? selectedDate;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    context.read<OrdersBloc>().add(LoadOrders());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardDismisser(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Buyurtmalar', style: TextStyle(color: Colors.white)),
          backgroundColor: AppColors.cFirstColor,
          foregroundColor: Colors.white,
          actions: [
            ///Commented out for now
            // IconButton(
            //   onPressed: _showFilterBottomSheet,
            //   icon: const Icon(Icons.filter_list),
            // ),
          ],
        ),
        body: Column(
          children: [
            // Search bar
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey[50],
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Mijoz nomi, telefon yoki buyurtma ID bo\'yicha qidirish...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            if (mounted) {
                              context.read<OrdersBloc>().add(SearchOrders(null));
                            }
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                onChanged: (value) {
                  setState(() {});
                  // Debounce search to avoid too many API calls
                  Future.delayed(const Duration(milliseconds: 500), () {
                    if (_searchController.text == value && mounted) {
                      context.read<OrdersBloc>().add(SearchOrders(value.isEmpty ? null : value));
                    }
                  });
                },
              ),
            ),
            // Orders list
            Expanded(
              child: BlocBuilder<OrdersBloc, OrdersState>(
                builder: (context, state) {
                  if (state.isLoading && state.orders.isEmpty) {
                    return const Center(child: CircularProgressIndicator());
                  }
      
                  if (state.orders.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.shopping_cart_outlined,
                              size: 64, color: Colors.grey),
                          const SizedBox(height: 16),
                          Text(
                            state.searchQuery != null && state.searchQuery!.isNotEmpty
                                ? 'Qidiruv bo\'yicha buyurtmalar topilmadi'
                                : 'Buyurtmalar topilmadi',
                            style: const TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                          if (state.searchQuery != null && state.searchQuery!.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            TextButton(
                              onPressed: () {
                                _searchController.clear();
                                if (mounted) {
                                  context.read<OrdersBloc>().add(SearchOrders(null));
                                }
                              },
                              child: const Text('Qidiruvni tozalash'),
                            ),
                          ],
                        ],
                      ),
                    );
                  }
      
                  return RefreshIndicator(
                    onRefresh: () async {
                      context.read<OrdersBloc>().add(RefreshOrders());
                    },
                    child: NotificationListener<ScrollNotification>(
                      onNotification: (ScrollNotification scrollInfo) {
                        if (!state.isLoadingMore &&
                            state.hasMore &&
                            scrollInfo.metrics.pixels ==
                                scrollInfo.metrics.maxScrollExtent) {
                          context.read<OrdersBloc>().add(LoadMoreOrders());
                        }
                        return false;
                      },
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16).copyWith(bottom: 80),
                        itemCount: state.orders.length + (state.hasMore ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == state.orders.length) {
                            return const Center(
                              child: Padding(
                                padding: EdgeInsets.all(16),
                                child: CircularProgressIndicator(),
                              ),
                            );
                          }
      
                          final order = state.orders[index];
                          return OrderCard(order: order, index: index);
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton.extended(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          onPressed: () => _showCreateOrderForm(context),
          backgroundColor: AppColors.cFirstColor,
          icon: const Icon(Icons.add, color: Colors.white),
          label: const Text('Buyurtma yaratish',
              style: TextStyle(color: Colors.white)),
        ),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (context) => FilterBottomSheet(
        selectedStatus: selectedStatus,
        selectedDate: selectedDate,
        onStatusChanged: (status) {
          setState(() => selectedStatus = status);
          context.read<OrdersBloc>().add(FilterOrdersByStatus(status));
        },
        onDateChanged: (date) {
          setState(() => selectedDate = date);
          context.read<OrdersBloc>().add(FilterOrdersByDate(date));
        },
      ),
    );
  }

  void _showCreateOrderForm(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateOrderPage(),
      ),
    );
  }
}

class OrderCard extends StatelessWidget {
  final Order order;
  final int index;

  const OrderCard({super.key, required this.order, required this.index});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => OrderDetailPage(order: order),
            ),
          );
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Thumbnail
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: order.photos.isNotEmpty &&
                          order.photos.first.isNotEmpty
                      ? CachedNetworkImage(
                          imageUrl: ApiPath.baseUrlFile + order.photos.first,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 80,
                            height: 80,
                            color: Colors.grey[300],
                            child: const Icon(Icons.image, color: Colors.grey),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 80,
                            height: 80,
                            color: Colors.grey[300],
                            child: const Icon(Icons.image, color: Colors.grey),
                          ),
                        )
                      : Container(
                          width: 80,
                          height: 80,
                          color: Colors.grey[300],
                          child: const Icon(Icons.image, color: Colors.grey),
                        ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        order.client?.fullName ?? 'Buyurtma #$index',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatDate(order.createdAt),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),

                      ///Comment out fow now
                      // Row(
                      //   children: [
                      //     Text(
                      //       '${order.totalPrice.toStringAsFixed(0)} so\'m',
                      //       style: const TextStyle(
                      //         fontWeight: FontWeight.w600,
                      //         fontSize: 15,
                      //       ),
                      //     ),
                      //     const Spacer(),
                      //     _buildStatusChip(order.status),
                      //   ],
                      // ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey,
                ),

                ///Commented out for now
                // Column(
                //   children: [
                //     _buildStatusChip(order.status),
                //     const SizedBox(height: 8),
                //     if (order.pdf != null)
                //       IconButton(
                //         onPressed: () => _downloadContract(order.pdf!),
                //         icon: const Icon(Icons.download, color: Colors.blue),
                //       ),
                //   ],
                // ),
              ],
            ),
          ],
        ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(int status) {
    Color color;
    String text;

    switch (status) {
      case 1:
        color = Colors.orange;
        text = 'Yangi';
        break;
      case 2:
        color = Colors.green;
        text = 'Yakunlangan';
        break;
      case 3:
        color = Colors.red;
        text = 'Bekor qilingan';
        break;
      default:
        color = Colors.grey;
        text = 'no-status';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year}';
  }

  void _downloadContract(String contractUrl) {
    // Implement contract download functionality
    print('Downloading contract: $contractUrl');
  }
}

class FilterBottomSheet extends StatelessWidget {
  final String? selectedStatus;
  final String? selectedDate;
  final Function(String?) onStatusChanged;
  final Function(String?) onDateChanged;

  const FilterBottomSheet({
    super.key,
    this.selectedStatus,
    this.selectedDate,
    required this.onStatusChanged,
    required this.onDateChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filtrlar',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          const Text('Status bo\'yicha:',
              style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip(
                  'Barchasi', null, selectedStatus, onStatusChanged),
              _buildFilterChip(
                  'Yangi', 'yangi', selectedStatus, onStatusChanged),
              _buildFilterChip('Yakunlangan', 'yakunlangan', selectedStatus,
                  onStatusChanged),
              _buildFilterChip('Bekor qilingan', 'bekor qilingan',
                  selectedStatus, onStatusChanged),
            ],
          ),
          const SizedBox(height: 16),
          const Text('Sana bo\'yicha:',
              style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate:
                          DateTime.now().subtract(const Duration(days: 365)),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      onDateChanged(date.toIso8601String().split('T')[0]);
                    }
                  },
                  icon: const Icon(Icons.calendar_today),
                  label: Text(selectedDate ?? 'Sana tanlang'),
                ),
              ),
              const SizedBox(width: 8),
              if (selectedDate != null)
                IconButton(
                  onPressed: () => onDateChanged(null),
                  icon: const Icon(Icons.clear),
                ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String? value, String? selectedValue,
      Function(String?) onChanged) {
    final isSelected = selectedValue == value;
    return FilterChip(
      label: Text(label,
          style: TextStyle(
              color: isSelected ? AppColors.cFirstColor : AppColors.black)),
      selected: isSelected,
      onSelected: (_) => onChanged(value),
      selectedColor: Colors.green[100],
      checkmarkColor: Colors.green,
    );
  }
}

class CreateOrderPage extends StatefulWidget {
  const CreateOrderPage({super.key});

  @override
  State<CreateOrderPage> createState() => _CreateOrderPageState();
}

class _CreateOrderPageState extends State<CreateOrderPage> {
  final PageController _pageController = PageController();
  int _currentStep = 0;

  // Form data
  Customer? selectedClient;
  List<File> photos = [];
  Position? currentPosition;
  PaymentType? paymentType;
  String? phoneNumber;
  String? smsCode;
  Uint8List? signature;
  File? contractFile;

  final SignatureController _signatureController = SignatureController(
    penStrokeWidth: 2,
    penColor: Colors.black,
    exportBackgroundColor: Colors.white,
  );

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year}';
  }

  @override
  void initState() {
    super.initState();

    // Clear any previous location state to ensure fresh selection
    _clearLocationState();

    _getCurrentLocation();
    // Load initial data
    context.read<OrdersBloc>().add(LoadProvinces());
    context.read<OrdersBloc>().add(LoadVarieties());
    // Initialize tree selection
    context.read<OrdersBloc>().add(InitializeTreeSelection());
    // Load user's default location preferences
    _loadUserLocationDefaults();
  }

  /// Clear location state to ensure fresh selection on each new order
  void _clearLocationState() {
    final ordersBloc = context.read<OrdersBloc>();

    // Clear selected locations in the bloc state
    ordersBloc.add(ClearAddressSelection());

    // Initialize fresh tree selection state
    ordersBloc.add(InitializeTreeSelection());
  }

  void _loadUserLocationDefaults() async {
    final storage = GetStorage();
    final defaultProvinceId = storage.read('user_default_province_id');
    final defaultRegionId = storage.read('user_default_region_id');
    final defaultDistrictId = storage.read('user_default_district_id');

    // Wait a bit for provinces to load, then set defaults
    Future.delayed(const Duration(milliseconds: 500), () {
      final state = context.read<OrdersBloc>().state;

      if (defaultProvinceId != null && state.provinces != null) {
        final defaultProvince = state.provinces!.firstWhere(
          (p) => p.id == defaultProvinceId,
          orElse: () => state.provinces!.first,
        );
        context.read<OrdersBloc>().add(SelectProvince(defaultProvince));

        // Set region default after province is selected
        if (defaultRegionId != null) {
          Future.delayed(const Duration(milliseconds: 300), () {
            final updatedState = context.read<OrdersBloc>().state;
            if (updatedState.regions != null &&
                updatedState.regions!.isNotEmpty) {
              Region? defaultRegion;
              try {
                defaultRegion = updatedState.regions!.firstWhere(
                  (r) => r.id == defaultRegionId,
                );
              } catch (e) {
                defaultRegion = updatedState.regions!.first;
              }

              context.read<OrdersBloc>().add(SelectRegion(defaultRegion));

              // Set district default after region is selected
              if (defaultDistrictId != null) {
                Future.delayed(const Duration(milliseconds: 300), () {
                  final finalState = context.read<OrdersBloc>().state;
                  if (finalState.districts != null &&
                      finalState.districts!.isNotEmpty) {
                    District? defaultDistrict;
                    try {
                      defaultDistrict = finalState.districts!.firstWhere(
                        (d) => d.id == defaultDistrictId,
                      );
                    } catch (e) {
                      defaultDistrict = finalState.districts!.first;
                    }

                    if (defaultDistrict != null) {
                      context
                          .read<OrdersBloc>()
                          .add(SelectDistrict(defaultDistrict));
                    }
                  }
                });
              }
            }
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            const Text('Yangi buyurtma', style: TextStyle(color: Colors.white)),
        backgroundColor: AppColors.cFirstColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            //Confirmation
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Chiqishni tasdiqlang'),
                content: const Text(
                    'Buyurtma saqlanmadi. Chiqishni tasdiqlaysizmi?'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Bekor qilish'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Tasdiqlash',
                        style: TextStyle(color: Colors.red)),
                  ),
                ],
              ),
            ).then((value) {
              if (value == true) {
                Navigator.pop(context);
              }
            });
          },
        ),
      ),
      body: BlocConsumer<OrdersBloc, OrdersState>(
        listener: (context, state) {
          if (state.error != null) {
            context.read<OrdersBloc>().clearError();
            _showTopSnackBar(state.error ?? '...', isError: true);
          }

          if (state.successMessage != null) {
            _showTopSnackBar("Muvaffaqiyatli yuborildi", isError: false);
            context.read<OrdersBloc>().clearSuccessMessage();
            Navigator.pop(context);
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value: (_currentStep + 1) / 6,
                backgroundColor: Colors.grey[300],
                minHeight: 10,
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppColors.cGreenishColor),
              ),
              Expanded(
                child: PageView(
                  controller: _pageController,
                  // Disable swiping on signature page to prevent accidental navigation
                  physics: NeverScrollableScrollPhysics(),
                  onPageChanged: (index) =>
                      setState(() => _currentStep = index),
                  children: [
                    _buildClientSelection(),
                    _buildPhotoCapture(),
                    _buildTreeSelection(),
                    _buildPaymentSelection(),
                    _buildSignatureCapture(),
                    _buildOrderSummary(),
                  ],
                ),
              ),
              _buildNavigationButtons(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildClientSelection() {
    return BlocBuilder<OrdersBloc, OrdersState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Mijoz va joylashuv ma\'lumotlari',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // Client Selection Card
              Card(
                child: ListTile(
                  leading: const Icon(Icons.person, color: Colors.green),
                  title: Text(selectedClient?.fullName ?? 'Mijoz tanlang'),
                  subtitle: selectedClient != null
                      ? Text(selectedClient!.phone)
                      : null,
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _selectClient,
                ),
              ),

              const SizedBox(height: 16),

              // Location Selection Section
              const Text(
                'Joylashuv tanlang',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),

              // Province Dropdown
              _buildProvinceDropdown(state),
              const SizedBox(height: 12),

              // Region Dropdown
              _buildRegionDropdown(state),
              const SizedBox(height: 12),

              // District Dropdown
              _buildDistrictDropdown(state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProvinceDropdown(OrdersState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Viloyat',
                style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            DropdownButtonFormField<Province>(
              value: _getValidProvince(state),
              decoration: const InputDecoration(
                hintText: 'Viloyatni tanlang',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: state.provinces?.map((province) {
                    return DropdownMenuItem<Province>(
                      value: province,
                      child: Text(province.title),
                    );
                  }).toList() ??
                  [],
              onChanged: state.isLoadingProvinces
                  ? null
                  : (Province? province) {
                      if (province != null) {
                        context
                            .read<OrdersBloc>()
                            .add(SelectProvince(province));
                        // Save user's province preference
                        GetStorage()
                            .write('user_default_province_id', province.id);
                      }
                    },
              validator: (value) => value == null ? 'Viloyatni tanlang' : null,
            ),
            if (state.isLoadingProvinces)
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegionDropdown(OrdersState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Tuman', style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            DropdownButtonFormField<Region>(
              value: _getValidRegion(state),
              decoration: const InputDecoration(
                hintText: 'Tumanni tanlang',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: state.regions?.map((region) {
                    return DropdownMenuItem<Region>(
                      value: region,
                      child: Text(region.title),
                    );
                  }).toList() ??
                  [],
              onChanged: (state.isLoadingRegions ||
                      state.selectedProvince == null)
                  ? null
                  : (Region? region) {
                      if (region != null) {
                        context.read<OrdersBloc>().add(SelectRegion(region));
                        // Save user's region preference
                        GetStorage().write('user_default_region_id', region.id);
                      }
                    },
              validator: (value) => value == null ? 'Tumanni tanlang' : null,
            ),
            if (state.isLoadingRegions)
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistrictDropdown(OrdersState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('MFY',
                style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            DropdownButtonFormField<District>(
              value: _getValidDistrict(state),
              decoration: const InputDecoration(
                hintText: 'MFY tanlang',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: state.districts?.map((district) {
                    return DropdownMenuItem<District>(
                      value: district,
                      child: Text(district.title),
                    );
                  }).toList() ??
                  [],
              onChanged:
                  (state.isLoadingDistricts || state.selectedRegion == null)
                      ? null
                      : (District? district) {
                          if (district != null) {
                            context
                                .read<OrdersBloc>()
                                .add(SelectDistrict(district));
                            // Save user's district preference
                            GetStorage()
                                .write('user_default_district_id', district.id);
                          }
                        },
              validator: (value) =>
                  value == null ? 'MFY' : null,
            ),
            if (state.isLoadingDistricts)
              const Padding(
                padding: EdgeInsets.only(top: 8),
                child: LinearProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoCapture() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Rasmlar',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const Text(
            'Uy eshigi va daraxt ekiladigan joy rasmlarini oling',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: photos.length + (photos.length < 2 ? 1 : 0),
              itemBuilder: (context, index) {
                if (index >= photos.length) {
                  return _buildPhotoPlaceholder();
                }
                return _buildPhotoItem(photos[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoPlaceholder() {
    return GestureDetector(
      onTap: _capturePhoto,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey, style: BorderStyle.solid),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.camera_alt, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text('Rasm olish', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoItem(File photo, int index) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.file(
            photo,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: GestureDetector(
            onTap: () => _removePhoto(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, color: Colors.white, size: 16),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTreeSelection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Daraxtlar',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: BlocBuilder<OrdersBloc, OrdersState>(
              builder: (context, state) {
                final treeProducts = state.orderTreeProducts ?? [];
                return ListView.builder(
                  padding: const EdgeInsets.only(bottom: 20),
                  itemCount: treeProducts.length + 1,
                  itemBuilder: (context, index) {
                    if (index == treeProducts.length) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        child: SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _addTreeProduct,
                            icon: const Icon(Icons.add),
                            label: const Text('Daraxt qo\'shish'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.cFirstColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.all(16),
                            ),
                          ),
                        ),
                      );
                    }
                    return TreeProductCard(
                      key: ValueKey('tree_product_${treeProducts[index].id}'),
                      product: treeProducts[index],
                      onChanged: (updatedProduct) {
                        context.read<OrdersBloc>().add(UpdateOrderTreeProduct(
                          productId: updatedProduct.id,
                          variety: updatedProduct.variety,
                          tree: updatedProduct.treeId.isNotEmpty ? Tree(
                            id: updatedProduct.treeId,
                            name: updatedProduct.treeType,
                            variety: updatedProduct.variety?.title ?? '',
                            price: updatedProduct.price,
                          ) : null,
                          count: updatedProduct.count,
                          price: updatedProduct.price,
                        ));
                      },
                      onRemove: () {
                        context.read<OrdersBloc>().add(RemoveTreeProductFromOrder(treeProducts[index].id));
                      },
                    );
                  },
                );
              },
            ),
          ),
          BlocBuilder<OrdersBloc, OrdersState>(
            builder: (context, state) {
              final totalPrice = context.read<OrdersBloc>().getTotalTreeProductsPrice();
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Jami summa:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      AppFunctions.formatMoneyWithCurrency(totalPrice),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.cFirstColor,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSelection() {
    //read phone number from get storage
    final phoneNumberStatic = GetStorage().read(PHONE);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'To\'lov usuli',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Card(
            child: Column(
              children: [
                RadioListTile<PaymentType>(
                  value: PaymentType.cash,
                  groupValue: paymentType,
                  onChanged: (value) => setState(() => paymentType = value),
                  title: const Text('Naqd pul'),
                  secondary: const Icon(Icons.payments, color: Colors.green),
                ),
                RadioListTile<PaymentType>(
                  value: PaymentType.credit,
                  groupValue: paymentType,
                  onChanged: (value) => setState(() => paymentType = value),
                  title: const Text('Nasiya'),
                  secondary:
                      const Icon(Icons.credit_card, color: Colors.orange),
                ),
              ],
            ),
          ),
          if (paymentType == PaymentType.credit) ...[
            const SizedBox(height: 16),
            Card(
              color: Colors.orange[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.credit_card, color: Colors.orange[600]),
                        const SizedBox(width: 8),
                        const Text(
                          'Nasiya to\'lovi',
                          style: TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      //readOnly: phoneNumberStatic == null ? false : true,
                      decoration: const InputDecoration(
                        labelText: 'Telefon raqam',
                        prefixText: '+998 ',
                        border: OutlineInputBorder(),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      keyboardType: TextInputType.phone,
                      initialValue: phoneNumberStatic?.formattedPhone ?? '',
                      onChanged: (value) => setState(() => phoneNumber = value),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed:
                            (phoneNumber != null && phoneNumber!.isNotEmpty)
                                ? _sendSmsCode
                                : null,
                        icon: const Icon(Icons.sms),
                        label: const Text('SMS kod yuborish'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange[600],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    if (phoneNumber != null && phoneNumber!.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      TextFormField(
                        decoration: InputDecoration(
                          labelText: 'SMS kod',
                          border: const OutlineInputBorder(),
                          filled: true,
                          fillColor: Colors.white,
                          helperText: 'Test uchun "1111" ni kiriting',
                          helperStyle: TextStyle(color: Colors.blue[600]),
                          suffixIcon: smsCode == '1111'
                              ? Icon(Icons.check_circle,
                                  color: AppColors.cFirstColor)
                              : null,
                        ),
                        keyboardType: TextInputType.number,
                        maxLength: 4,
                        onChanged: (value) => setState(() => smsCode = value),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSignatureCapture() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with clear button
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Elektron imzo',
                      style:
                          TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                    const Text(
                      'Iltimos, pastdagi maydoncha ichida imzo chizing',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
              OutlinedButton.icon(
                onPressed: () {
                  _signatureController.clear();
                  setState(() => signature = null);
                },
                icon: const Icon(Icons.clear),
                label: const Text('Tozalash'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Signature area
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.cFirstColor.withOpacity(0.04),
                border: Border.all(color: AppColors.cFirstColor, width: 2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Signature(
                  dynamicPressureSupported: true,
                  controller: _signatureController,
                  width: 500,
                  height: 300,
                  backgroundColor: Colors.white,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Info text
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Imzo avtomatik ravishda saqlanadi. Keyingi bosqichga o\'tish uchun "Keyingisi" tugmasini bosing.',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 13,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Buyurtma xulosasi',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Client Information Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.person,
                                  color: AppColors.cFirstColor, size: 20),
                              const SizedBox(width: 8),
                              const Text(
                                'Mijoz ma\'lumotlari',
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          _buildSummaryRow(
                              'F.I.Sh:', selectedClient?.fullName ?? ''),
                          _buildSummaryRow(
                              'Telefon:', selectedClient?.phone ?? ''),
                          _buildSummaryRow(
                              'Manzil:', selectedClient?.address ?? ''),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 5),

                  // Photos Section
                  if (photos.isNotEmpty)
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.photo_camera,
                                    color: Colors.blue[600], size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'Rasmlar (${photos.length} ta)',
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              height: 80,
                              child: ListView.builder(
                                padding: const EdgeInsets.only(bottom: 20),
                                scrollDirection: Axis.horizontal,
                                itemCount: photos.length,
                                itemBuilder: (context, index) {
                                  return Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.file(
                                        photos[index],
                                        width: 80,
                                        height: 80,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  const SizedBox(height: 5),

                  // Trees Section
                  BlocBuilder<OrdersBloc, OrdersState>(
                    builder: (context, state) {
                      final treeProducts = state.orderTreeProducts ?? [];
                      if (treeProducts.isEmpty) return const SizedBox.shrink();

                      return Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.park,
                                      color: AppColors.cFirstColor, size: 20),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Daraxtlar (${treeProducts.length} ta)',
                                    style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              ...treeProducts.asMap().entries.map((entry) {
                                final index = entry.key;
                                final product = entry.value;
                                return Container(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  padding: const EdgeInsets.all(12),
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: Colors.green[50],
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.green[200]!),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '${index + 1}. ${product.treeType}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      if (product.variety != null)
                                        Text('Nav: ${product.variety!.title}'),
                                      Text('Soni: ${product.count} dona'),
                                      Text(
                                          'Narxi: ${product.price.toStringAsFixed(0)} so\'m/dona'),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Jami: ${product.totalPrice.f} so\'m',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.green[700],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                            ],
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 5),

                  // Payment Information
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.payment,
                                  color: Colors.orange[600], size: 20),
                              const SizedBox(width: 8),
                              const Text(
                                'To\'lov ma\'lumotlari',
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 5),
                          _buildSummaryRow(
                              'To\'lov usuli:',
                              paymentType == PaymentType.cash
                                  ? 'Naqd pul'
                                  : 'Nasiya'),
                          if (paymentType == PaymentType.credit &&
                              phoneNumber != null)
                            _buildSummaryRow(
                                'Telefon raqam:', '+998 $phoneNumber'),
                          const SizedBox(height: 8),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.green[100],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.green[300]!),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'JAMI SUMMA:',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                BlocBuilder<OrdersBloc, OrdersState>(
                                  builder: (context, state) {
                                    final totalPrice = context.read<OrdersBloc>().getTotalTreeProductsPrice();
                                    return Text(
                                      AppFunctions.formatMoneyWithCurrency(totalPrice),
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green[700],
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 5),

                  // Signature Section
                  if (signature != null)
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.draw,
                                    color: Colors.purple[600], size: 20),
                                const SizedBox(width: 8),
                                const Text(
                                  'Elektron imzo',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Center(
                              child: Container(
                                width: double.infinity,
                                height: 100,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey[300]!),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.memory(
                                    signature!,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons(OrdersState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, -2),
            blurRadius: 8,
            color: Colors.black.withOpacity(0.1),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                child: const Text('Orqaga'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: state.isCreating ? null : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.cFirstColor,
                foregroundColor: Colors.white,
              ),
              child: state.isCreating
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child:
                          const CircularProgressIndicator(color: Colors.white))
                  : Text(_currentStep == 5 ? 'Yaratish' : 'Keyingisi'),
            ),
          ),
        ],
      ),
    );
  }

  void _nextStep() {
    if (_validateCurrentStep()) {
      if (_currentStep < 5) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        _createOrder();
      }
    }
  }

  void _previousStep() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        if (selectedClient == null) {
          _showTopSnackBar('Mijoz tanlang', isError: true);
          return false;
        }

        final state = context.read<OrdersBloc>().state;
        if (state.selectedProvince == null) {
          _showTopSnackBar('Viloyatni tanlang', isError: true);
          return false;
        }
        if (state.selectedRegion == null) {
          _showTopSnackBar('Tumanni tanlang', isError: true);
          return false;
        }
        if (state.selectedDistrict == null) {
          _showTopSnackBar('MFY', isError: true);
          return false;
        }
        return true;
      case 1:
        if (photos.isEmpty) {
          _showTopSnackBar('Kamida 1 ta rasm oling', isError: true);
          return false;
        }
        return true;
      case 2:
        final ordersBloc = context.read<OrdersBloc>();
        final treeProducts = ordersBloc.state.orderTreeProducts ?? [];
        if (treeProducts.isEmpty) {
          _showTopSnackBar('Kamida 1 ta daraxt turi qo\'shing', isError: true);
          return false;
        }

        // Validate tree products comprehensively
        return _validateTreeProducts();
      case 3:
        if (paymentType == null) {
          _showTopSnackBar('To\'lov usulini tanlang', isError: true);
          return false;
        }
        if (paymentType == PaymentType.credit) {
          if (phoneNumber == null || phoneNumber!.isEmpty) {
            _showTopSnackBar('Telefon raqamni kiriting', isError: true);
            return false;
          }
          if (smsCode == null || smsCode!.isEmpty) {
            _showTopSnackBar('SMS kodni kiriting', isError: true);
            return false;
          }
          if (!_validateSmsCode()) {
            _showTopSnackBar(
                'SMS kod noto\'g\'ri. Test uchun "1111" ni kiriting.',
                isError: true);
            return false;
          }
        }
        return true;
      case 4:
        // Automatically capture signature if controller has content
        if (_signatureController.isNotEmpty) {
          _captureSignature();
          return true;
        } else {
          _showTopSnackBar('Imzo chizing', isError: true);
          return false;
        }
      default:
        return true;
    }
  }

  void _showTopSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.only(
          bottom: 80,
          left: 20,
          right: 20,
        ),
        backgroundColor: isError ? Colors.red[600] : AppColors.cGreenishColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showTopSnackBar('Joylashuv xizmati o\'chirilgan. Iltimos yoqing.',
            isError: true);
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showTopSnackBar('Joylashuv ruxsati berilmagan', isError: true);
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showTopSnackBar(
            'Joylashuv ruxsati doimiy ravishda rad etilgan. Sozlamalardan yoqing.',
            isError: true);
        return;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
      setState(() => currentPosition = position);
    } catch (e) {
      print('Error getting location: $e');
      _showTopSnackBar('Joylashuvni olishda xatolik: ${e.toString()}');
    }
  }

  void _selectClient() async {
    final client = await showModalBottomSheet<Customer>(
      context: context,
      isScrollControlled: true,
      builder: (context) => const ClientSelectorBottomSheet(),
    );
    if (client != null) {
      setState(() => selectedClient = client);
      // Set location defaults from selected client
      _setDefaultsFromSelectedClient();
    }
  }

  Future<void> _capturePhoto() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.camera,
      imageQuality: 85,
    );
    if (image != null) {
      setState(() => photos.add(File(image.path)));
    }
  }

  void _removePhoto(int index) {
    setState(() => photos.removeAt(index));
  }

  void _addTreeProduct() {
    context.read<OrdersBloc>().add(AddTreeProductToOrder());
  }

  double _calculateTotalPrice() {
    return context.read<OrdersBloc>().getTotalTreeProductsPrice();
  }

  void _sendSmsCode() {
    if (phoneNumber != null && phoneNumber!.isNotEmpty) {
      // NOTE: Mock SMS sending for testing purposes
      // In production, this would integrate with a real SMS service
      setState(() {
        // Mark that SMS was sent
      });

      _showTopSnackBar('SMS kod yuborildi. Test uchun "1111" ni kiriting.');
    } else {
      _showTopSnackBar('Telefon raqamni kiriting', isError: true);
    }
  }

  bool _validateSmsCode() {
    // NOTE: Mock validation for testing purposes
    // In production, this would validate against the actual SMS code sent
    return smsCode == '1111';
  }

  /// Comprehensive tree product validation
  bool _validateTreeProducts() {
    final ordersBloc = context.read<OrdersBloc>();
    final treeProducts = ordersBloc.state.orderTreeProducts ?? [];

    if (treeProducts.isEmpty) {
      _showTopSnackBar('Kamida 1 ta daraxt turi qo\'shing');
      return false;
    }

    List<TreeProduct> incompleteProducts = [];
    List<TreeProduct> limitExceededProducts = [];
    List<TreeProduct> validProducts = [];

    for (int i = 0; i < treeProducts.length; i++) {
      final product = treeProducts[i];

      // Check if product is incomplete
      bool isIncomplete = product.treeId.isEmpty ||
          product.treeType.isEmpty ||
          product.count <= 0 ||
          product.price <= 0 ||
          product.variety == null;

      if (isIncomplete) {
        incompleteProducts.add(product);
        continue;
      }

      // Check quantity limits - get tree from available trees
      final availableTrees = ordersBloc.getAvailableTreesForProduct(product.id);
      Tree? selectedTree;
      try {
        selectedTree = availableTrees.firstWhere((tree) => tree.id == product.treeId);
      } catch (e) {
        // Tree not found, treat as incomplete
        incompleteProducts.add(product);
        continue;
      }

      bool limitExceeded = selectedTree.count != null &&
          selectedTree.count! > 0 &&
          product.count > selectedTree.count!;

      if (limitExceeded) {
        limitExceededProducts.add(product);
        continue;
      }

      validProducts.add(product);
    }

    // Handle limit exceeded products - prevent navigation
    if (limitExceededProducts.isNotEmpty) {
      final productNames = limitExceededProducts
          .map((p) => p.treeType.isNotEmpty
              ? p.treeType
              : 'Daraxt ${limitExceededProducts.indexOf(p) + 1}')
          .join(', ');
      _showTopSnackBar('Mavjud miqdordan ko\'p: $productNames', isError: true);
      return false;
    }

    // Handle incomplete products intelligently
    if (incompleteProducts.isNotEmpty) {
      // If we have at least one valid product, remove incomplete ones and continue
      if (validProducts.isNotEmpty) {
        // Remove incomplete products using BLoC
        for (final incompleteProduct in incompleteProducts) {
          context.read<OrdersBloc>().add(RemoveTreeProductFromOrder(incompleteProduct.id));
        }

        final removedCount = incompleteProducts.length;
        _showTopSnackBar(
            '$removedCount ta to\'liq bo\'lmagan daraxt o\'chirildi',
            isError: true);
        return true;
      } else {
        // No valid products, prevent navigation
        _showTopSnackBar('Barcha daraxt ma\'lumotlarini to\'ldiring',
            isError: true);
        return false;
      }
    }

    // All products are valid
    return true;
  }

  Future<void> _captureSignature() async {
    if (_signatureController.isNotEmpty) {
      final signatureBytes = await _signatureController.toPngBytes();
      setState(() => signature = signatureBytes);
    }
  }

  Future<void> _createOrder() async {
    if (currentPosition == null) {
      _showTopSnackBar('Joylashuv ma\'lumoti olinmayapti', isError: true);
      return;
    }

    // Generate PDF contract
    // await _generateContract();

    final state = context.read<OrdersBloc>().state;
    final treeProducts = state.orderTreeProducts ?? [];

    final request = CreateOrderRequest(
      client: selectedClient!.id,
      totalPrice: _calculateTotalPrice(),
      paymentType: paymentType == PaymentType.cash ? 1 : 2,
      province: state.selectedProvince?.id ?? '',
      region: state.selectedRegion?.id ?? '',
      district: state.selectedDistrict?.id ?? '',
      location: Location(
        latitude: currentPosition!.latitude.toString(),
        longitude: currentPosition!.longitude.toString(),
      ),
      products: treeProducts,
      photos: photos,
      // contractFile: contractFile,
      signature: signature,
    );

    context.read<OrdersBloc>().add(CreateOrder(request));
  }

  Future<void> _generateContract() async {
    try {
      final state = context.read<OrdersBloc>().state;
      final treeProducts = state.orderTreeProducts ?? [];
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.green100,
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'DARAXT SOTISH SHARTNOMASI',
                        style: pw.TextStyle(
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'Shartnoma raqami: ${DateTime.now().millisecondsSinceEpoch}',
                        style: const pw.TextStyle(fontSize: 14),
                      ),
                      pw.Text(
                        'Sana: ${_formatDate(DateTime.now())}',
                        style: const pw.TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Client information
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey300),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'MIJOZ MA\'LUMOTLARI',
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text('F.I.Sh: ${selectedClient!.fullName}'),
                      pw.Text('Telefon: ${selectedClient!.phone}'),
                      pw.Text('Manzil: ${selectedClient!.address}'),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Products table
                pw.Container(
                  width: double.infinity,
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'SOTILAYOTGAN DARAXTLAR',
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Table(
                        border: pw.TableBorder.all(),
                        children: [
                          pw.TableRow(
                            children: [
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Daraxt turi',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Nav',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Soni',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Narxi',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                              pw.Padding(
                                padding: const pw.EdgeInsets.all(8),
                                child: pw.Text('Jami',
                                    style: pw.TextStyle(
                                        fontWeight: pw.FontWeight.bold)),
                              ),
                            ],
                          ),
                          ...treeProducts
                              .map((product) => pw.TableRow(
                                    children: [
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child: pw.Text(product.treeType),
                                      ),
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child: pw.Text(product.variety?.title ?? ''),
                                      ),
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child:
                                            pw.Text(product.count.toString()),
                                      ),
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child: pw.Text(
                                            '${product.price.toStringAsFixed(0)} so\'m'),
                                      ),
                                      pw.Padding(
                                        padding: const pw.EdgeInsets.all(8),
                                        child: pw.Text(
                                            '${product.totalPrice.toStringAsFixed(0)} so\'m'),
                                      ),
                                    ],
                                  ))
                              .toList(),
                        ],
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Total
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.green50,
                    border: pw.Border.all(color: PdfColors.green300),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'JAMI SUMMA:',
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Text(
                        '${_calculateTotalPrice().toStringAsFixed(0)} so\'m',
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.green800,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // Payment method
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey300),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'TO\'LOV USULI',
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(paymentType == PaymentType.cash
                          ? 'Naqd pul'
                          : 'Nasiya'),
                    ],
                  ),
                ),

                pw.Spacer(),

                // Signature section
                if (signature != null)
                  pw.Container(
                    width: double.infinity,
                    child: pw.Column(
                      children: [
                        pw.Text(
                          'MIJOZ IMZOSI',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.SizedBox(height: 10),
                        pw.Container(
                          height: 80,
                          width: 200,
                          decoration: pw.BoxDecoration(
                            border: pw.Border.all(),
                          ),
                          child: pw.Center(
                            child: pw.Image(
                              pw.MemoryImage(signature!),
                              fit: pw.BoxFit.contain,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                pw.SizedBox(height: 20),

                // Footer
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.grey100,
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    'Ushbu shartnoma ${_formatDate(DateTime.now())} sanada tuzilgan va ikki tomon o\'rtasida kelishilgan.',
                    style: const pw.TextStyle(fontSize: 12),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );

      final bytes = await pdf.save();
      final tempDir = await getTemporaryDirectory();
      final file = File(
          '${tempDir.path}/contract_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(bytes);

      setState(() => contractFile = file);
    } catch (e) {
      print('Error generating PDF: $e');
      _showTopSnackBar('Shartnoma yaratishda xatolik', isError: true);
    }
  }

  Province? _getValidProvince(OrdersState state) {
    if (state.selectedProvince == null || state.provinces == null) return null;
    try {
      return state.provinces!
          .firstWhere((p) => p.id == state.selectedProvince!.id);
    } catch (e) {
      return null;
    }
  }

  Region? _getValidRegion(OrdersState state) {
    // If no region is selected or regions list is null/empty, return null
    if (state.selectedRegion == null ||
        state.regions == null ||
        state.regions!.isEmpty) {
      return null;
    }

    // If province is not selected, region should also be null
    if (state.selectedProvince == null) {
      return null;
    }

    try {
      return state.regions!.firstWhere((r) => r.id == state.selectedRegion!.id);
    } catch (e) {
      // If selected region is not found in current regions list, return null
      return null;
    }
  }

  District? _getValidDistrict(OrdersState state) {
    // If no district is selected or districts list is null/empty, return null
    if (state.selectedDistrict == null ||
        state.districts == null ||
        state.districts!.isEmpty) {
      return null;
    }

    // If region is not selected, district should also be null
    if (state.selectedRegion == null) {
      return null;
    }

    try {
      return state.districts!
          .firstWhere((d) => d.id == state.selectedDistrict!.id);
    } catch (e) {
      // If selected district is not found in current districts list, return null
      return null;
    }
  }

  void _setDefaultsFromSelectedClient() {
    if (selectedClient != null) {
      // Set province default from selectedClient
      if (selectedClient!.province != null) {
        context
            .read<OrdersBloc>()
            .add(SelectProvince(selectedClient!.province!));
      }

      // Set region default from selectedClient
      if (selectedClient!.region != null) {
        context.read<OrdersBloc>().add(SelectRegion(selectedClient!.region!));
      }
    }
  }

  @override
  void dispose() {
    _signatureController.dispose();
    _pageController.dispose();
    super.dispose();
  }
}

// Supporting widgets and models
class TreeProductCard extends StatefulWidget {
  final TreeProduct product;
  final Function(TreeProduct) onChanged;
  final VoidCallback onRemove;

  const TreeProductCard({
    super.key,
    required this.product,
    required this.onChanged,
    required this.onRemove,
  });

  @override
  State<TreeProductCard> createState() => _TreeProductCardState();
}

// Isolated dropdown widget for varieties
class _IsolatedVarietyDropdown extends StatefulWidget {
  final String productId;
  final TreeVariety? initialValue;
  final Function(TreeVariety?) onChanged;

  const _IsolatedVarietyDropdown({
    required this.productId,
    this.initialValue,
    required this.onChanged,
  });

  @override
  State<_IsolatedVarietyDropdown> createState() =>
      _IsolatedVarietyDropdownState();
}

class _IsolatedVarietyDropdownState extends State<_IsolatedVarietyDropdown> {
  List<TreeVariety> _varieties = [];
  TreeVariety? _selectedVariety;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedVariety = widget.initialValue;
    _loadVarieties();
  }

  Future<void> _loadVarieties() async {
    if (_varieties.isNotEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get varieties from BLoC
      final context = this.context;
      if (context.mounted) {
        final ordersBloc = context.read<OrdersBloc>();
        final response = await ordersBloc.orderRemoteDatasource
            .getAllVarieties(page: 1, limit: 50);

        if (mounted) {
          setState(() {
            _varieties = response.varieties;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _varieties = [];
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DropdownButtonFormField<TreeVariety>(
          key: ValueKey('isolated_variety_${widget.productId}'),
          value: _selectedVariety != null &&
                  _varieties.any((v) => v.id == _selectedVariety!.id)
              ? _selectedVariety
              : null,
          decoration: const InputDecoration(
            hintText: 'Daraxtni tanlang',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: _isLoading
              ? []
              : _varieties.map((variety) {
                  return DropdownMenuItem<TreeVariety>(
                    value: variety,
                    child: Text(variety.title),
                  );
                }).toList(),
          onChanged: _isLoading
              ? null
              : (TreeVariety? variety) {
                  setState(() {
                    _selectedVariety = variety;
                  });
                  widget.onChanged(variety);
                },
        ),
        if (_isLoading)
          const Padding(
            padding: EdgeInsets.only(top: 8),
            child: LinearProgressIndicator(),
          ),
      ],
    );
  }
}

// Isolated dropdown widget for trees
class _IsolatedTreeDropdown extends StatefulWidget {
  final String productId;
  final String? varietyId;
  final Tree? initialValue;
  final List<Tree> availableTrees;
  final bool isLoading;
  final Function(Tree?) onChanged;

  const _IsolatedTreeDropdown({
    required this.productId,
    this.varietyId,
    this.initialValue,
    required this.availableTrees,
    required this.isLoading,
    required this.onChanged,
  });

  @override
  State<_IsolatedTreeDropdown> createState() => _IsolatedTreeDropdownState();
}

class _IsolatedTreeDropdownState extends State<_IsolatedTreeDropdown> {
  Tree? _selectedTree;

  @override
  void initState() {
    super.initState();
    _selectedTree = widget.initialValue;
  }

  @override
  void didUpdateWidget(_IsolatedTreeDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update selected tree if it's no longer in available trees
    if (widget.availableTrees != oldWidget.availableTrees) {
      if (_selectedTree != null && !widget.availableTrees.any((tree) => tree.id == _selectedTree!.id)) {
        setState(() {
          _selectedTree = null;
        });
        widget.onChanged(null);
      }
    }

    // Update initial value if changed
    if (widget.initialValue != oldWidget.initialValue) {
      setState(() {
        _selectedTree = widget.initialValue;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DropdownButtonFormField<Tree>(
          key: ValueKey('isolated_tree_${widget.productId}'),
          isExpanded: true,
          value: _selectedTree != null && widget.availableTrees.any((t) => t.id == _selectedTree!.id)
              ? _selectedTree
              : null,
          decoration: const InputDecoration(
            hintText: 'Daraxt navini tanlang',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
          items: widget.isLoading
              ? []
              : widget.availableTrees.map((tree) {
                  return DropdownMenuItem<Tree>(
                    value: tree,
                    child: Text(tree.name,
                        style: const TextStyle(fontWeight: FontWeight.w500)),
                  );
                }).toList(),
          onChanged: widget.isLoading
              ? null
              : (Tree? tree) {
                  setState(() {
                    _selectedTree = tree;
                  });
                  widget.onChanged(tree);
                },
        ),
        if (widget.isLoading)
          const Padding(
            padding: EdgeInsets.only(top: 8),
            child: LinearProgressIndicator(),
          ),
      ],
    );
  }
}



class _TreeProductCardState extends State<TreeProductCard> {
  TreeVariety? selectedVariety;
  Tree? selectedTree;
  String? currentVarietyId;
  late TextEditingController _quantityController;

  @override
  void initState() {
    super.initState();
    // Initialize quantity controller
    _quantityController = TextEditingController(text: widget.product.count.toString());

    // Initialize from existing product data if available
    selectedVariety = widget.product.variety;
    currentVarietyId = selectedVariety?.id;

    // If we have a tree ID, try to find the tree in available trees
    if (widget.product.treeId.isNotEmpty) {
      final ordersBloc = context.read<OrdersBloc>();
      final availableTrees = ordersBloc.getAvailableTreesForProduct(widget.product.id);
      try {
        selectedTree = availableTrees.firstWhere((tree) => tree.id == widget.product.treeId);
      } catch (e) {
        // Tree not found in available trees, will be loaded when variety is selected
      }
    }
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrdersBloc, OrdersState>(
      builder: (context, state) {
        // Validate and clean up state if needed
        _validateAndCleanupState(state);
        return KeyboardDismisser(
          child: Card(
            margin: const EdgeInsets.only(bottom: 12),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with delete button
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          selectedTree?.name ?? 'Daraxt tanlang',
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                      ),
                      IconButton(
                        onPressed: widget.onRemove,
                        icon: const Icon(Icons.delete, color: Colors.red),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Variety Selection
                  const Text('Daraxt',
                      style: TextStyle(fontWeight: FontWeight.w500)),
                  const SizedBox(height: 8),
                  _IsolatedVarietyDropdown(
                    productId: widget.product.id,
                    initialValue: selectedVariety,
                    onChanged: (TreeVariety? variety) {
                      setState(() {
                        selectedVariety = variety;
                        currentVarietyId = variety?.id;
                        selectedTree = null; // Clear tree selection when variety changes
                      });

                      // Load trees for this variety and product
                      if (variety != null) {
                        context.read<OrdersBloc>().add(LoadTreesForOrderProduct(
                          productId: widget.product.id,
                          varietyId: variety.id,
                        ));
                      }

                      // Update product
                      _updateProduct();
                    },
                  ),

                  const SizedBox(height: 12),

                  // Tree Selection
                  const Text('Daraxt turi (nav)',
                      style: TextStyle(fontWeight: FontWeight.w500)),
                  const SizedBox(height: 8),
                  BlocBuilder<OrdersBloc, OrdersState>(
                    builder: (context, state) {
                      final isLoading = state.treeLoadingStatesByProduct?[widget.product.id] ?? false;
                      final availableTrees = state.availableTreesByProduct?[widget.product.id] ?? [];

                      return _IsolatedTreeDropdown(
                        productId: widget.product.id,
                        varietyId: currentVarietyId,
                        initialValue: selectedTree,
                        availableTrees: availableTrees,
                        isLoading: isLoading,
                        onChanged: (Tree? tree) {
                          setState(() {
                            selectedTree = tree;
                          });

                          // Update product
                          _updateProduct();
                        },
                      );
                    },
                  ),

                  const SizedBox(height: 12),

                  // Quantity and Price
                  Row(
                    children: [
                      //Make additional +- buttons
                      Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: EdgeInsets.zero,
                              margin: EdgeInsets.zero,
                            height: 50,
                            width: 40,
                            decoration: BoxDecoration(
                              color: AppColors.cFirstColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: InkWell(
                              onTap: () {
                                final count = int.tryParse(widget.product.count.toString()) ?? 0;
                                if (count > 1) {
                                  _updateProduct(count: count - 1);
                                  HapticFeedback.lightImpact();
                                }
                              },
                              child: Icon(
                                Icons.remove,
                                color: Colors.grey,
                              ),
                            )),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextFormField(
                                key: ValueKey('quantity_${widget.product.id}'),
                                decoration: InputDecoration(
                                  border: OutlineInputBorder()
                                ),
                                keyboardType: TextInputType.number,
                                initialValue: widget.product.count.toString(),
                                validator: (value) {
                                  final count = int.tryParse(value ?? '') ?? 0;
                                  if (count <= 0) {
                                    return 'Soni 0 dan katta bo\'lishi kerak';
                                  }
                                  return null;
                                },
                                onChanged: (value) {
                                  final count = int.tryParse(value) ?? 1;
                                  _updateProduct(count: count);
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                                padding: EdgeInsets.zero,
                                margin: EdgeInsets.zero,
                                height: 50,
                                width: 40,
                                decoration: BoxDecoration(
                                  color: AppColors.cFirstColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: InkWell(
                                  onTap: () {
                                    final count = int.tryParse(widget.product.count.toString()) ?? 0;
                                    _updateProduct(count: count + 1);
                                    HapticFeedback.lightImpact();
                                  },
                                  child: Icon(
                                    Icons.add,
                                    color: Colors.grey,
                                  ),
                                )),
                            const SizedBox(width: 8),
                          ],
                        ),
                      ),
                      Expanded(
                        child: TextFormField(
                          key: ValueKey(selectedTree?.id),
                          decoration: const InputDecoration(
                            labelText: 'Birlik narxi (So\'m)',
                            border: OutlineInputBorder(),
                          ),
                          style: TextStyle(color: Colors.grey),
                          keyboardType: TextInputType.number,
                          readOnly: true,
                          // Auto-calculated from selected tree
                          initialValue:
                              selectedTree?.price != null
                                  ? AppFunctions.formatMoney(selectedTree!.price)
                                  : AppFunctions.formatMoney(widget.product.price),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(children: [
                    Text(selectedTree != null &&
                        selectedTree!.count != null &&
                        selectedTree!.count! > 0
                        ? '- Mavjud: ${selectedTree!.count} dona'
                        : selectedTree != null
                        ? '- Cheksiz mavjud'
                        : '- Daraxtni tanlang')
                  ],),

                  const SizedBox(height: 8),

                  // Total Price
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Jami: ${AppFunctions.formatMoneyWithCurrency(widget.product.totalPrice)}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.right,
                    ),
                  ),

                  // Loading indicators
                  if (state.isLoadingVarieties)
                    const Padding(
                      padding: EdgeInsets.only(top: 8),
                      child: LinearProgressIndicator(),
                    ),

                  // Show loading indicator if trees are being loaded for this product
                  if (state.treeLoadingStatesByProduct?[widget.product.id] == true)
                    const Padding(
                      padding: EdgeInsets.only(top: 8),
                      child: Row(
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Daraxtlar yuklanmoqda...', style: TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Validate and cleanup state if selections are no longer valid
  void _validateAndCleanupState(OrdersState state) {
    // Since we're using isolated dropdowns, minimal validation is needed
    // The isolated dropdowns handle their own state validation
  }

  void _updateProduct({int? count, double? price}) {
    // Use the selected tree's unit price if available
    final unitPrice = selectedTree?.price ?? price ?? widget.product.price;
    final quantity = count ?? widget.product.count;

    final updatedProduct = widget.product.copyWith(
      treeId: selectedTree?.id ?? widget.product.treeId,
      treeType: selectedTree?.name ?? widget.product.treeType,
      variety: selectedVariety ?? widget.product.variety,
      count: quantity,
      price: unitPrice,
      totalPrice: quantity * unitPrice,
    );
    widget.onChanged(updatedProduct);
  }
}

enum PaymentType { cash, credit }

// Client Selector Bottom Sheet
class ClientSelectorBottomSheet extends StatefulWidget {
  const ClientSelectorBottomSheet({super.key});

  @override
  State<ClientSelectorBottomSheet> createState() =>
      _ClientSelectorBottomSheetState();
}

class _ClientSelectorBottomSheetState extends State<ClientSelectorBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  final CustomerService _customerService = CustomerService(
    dio: di.di(),
    storage: di.di(),
    networkInfo: di.di(),
  );

  List<Customer> clients = [];
  bool isLoading = false;
  String? searchQuery;

  @override
  void initState() {
    super.initState();
    _loadClients();
    _searchController.addListener(() {
      if (_searchController.text != searchQuery) {
        searchQuery = _searchController.text;
        _loadClients();
      }
    });
  }

  Future<void> _loadClients() async {
    setState(() => isLoading = true);

    try {
      final response = await _customerService.getCustomers(
        page: 1,
        limit: 50, // Load more clients for selection
        search: searchQuery?.isNotEmpty == true ? searchQuery : null,
        forceRefresh: false,
      );

      setState(() {
        clients = response?.docs ?? [];
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
        clients = [];
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Mijozlarni yuklashda xatolik: $e',
                style: const TextStyle(color: Colors.white)),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height - 150,
              left: 20,
              right: 20,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Title
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Mijoz tanlang',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ),

          // Search field
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Mijoz qidirish...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Clients list
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : ListView.builder(
                    shrinkWrap: true,
                    itemCount: clients.length,
                    padding: const EdgeInsets.only(bottom: 20),
                    itemBuilder: (context, index) {
                      final client = clients[index];
                      return ListTile(
                        leading: CircleAvatar(
                          radius: 30,
                          backgroundColor: Colors.green[100],
                          child: Text(
                            //Only first letter
                            client.fullName[0].toUpperCase(),
                            style: TextStyle(color: AppColors.cFirstColor),
                          ),
                        ),
                        title: Text(client.fullName),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(client.phone),
                            Text(
                              client.address,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        onTap: () => Navigator.pop(context, client),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
